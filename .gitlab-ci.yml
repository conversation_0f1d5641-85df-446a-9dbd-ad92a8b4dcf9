stages:          # List of stages for jobs, and their order of execution
  - deploy

# image: samueldebruyn/debian-git

before_script:
  - apt-get update
  - apt-get -qq install git-ftp

deploy_staging:
  stage: deploy
  script:
    - echo "Deploy to staging server"
    # - git ftp catchup --user $STAGING_FTP_USERNAME --passwd $STAGING_FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html/sonoma
    - git ftp push --user $STAGING_FTP_USERNAME --passwd $STAGING_FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html/sonoma
  environment:
    name: staging
    url: http://sonoma.wearetesting.co.uk
  only:
  - staging

deploy_production:
  stage: deploy
  script:
    - echo "Deploy to production server"
    # - git ftp catchup --user $FTP_USERNAME --passwd $FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html
    - git ftp push --user $FTP_USERNAME --passwd $FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html
  environment:
    name: production
    url: https://sonomagatwick.com
  only:
  - master
