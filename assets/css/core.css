@font-face {
  font-family: "LMMonoPropLt10-Bold";
  src: url("../fonts/lmmonoproplt10-bold.woff2") format("woff2"), url("../fonts/lmmonoproplt10-bold.woff") format("woff"), url("../fonts/lmmonoproplt10-bold.otf") format("opentype");
  font-style: normal;
  font-weight: 400;
}
@font-face {
  font-family: "Moon Flower Bold";
  src: url("../fonts/Moon Flower Bold.woff2") format("woff2"), url("../fonts/Moon Flower Bold.woff") format("woff"), url("../fonts/Moon Flower Bold.otf") format("opentype");
  font-style: normal;
  font-weight: 400;
}
body {
  font-size: .9375rem;
  color: rgba(0, 0, 0, 0.7);
  font-family: "LMMonoPropLt10-Bold", Georgia, 'Times New Roman', serif;
  line-height: 1.33;
}
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4 {
  font-family: "Moon Flower Bold", Arial, Helvetica, sans-serif;
  line-height: 1;
  margin-bottom: 0;
}
h1,
.h1 {
  font-size: 3.125rem;
}
h2,
.h2 {
  font-size: 2.5rem;
}
h3,
.h3 {
  font-size: 1.875rem;
}
h4,
.h4 {
  font-size: 1.3125rem;
}
.lead {
  font-size: 1.1875rem;
}
.big {
  font-size: 3.625rem;
  letter-spacing: 0.02em;
  line-height: 1;
}
a {
  color: inherit;
  text-decoration: none;
  transition: all 0.35s ease-in-out;
}
a:hover,
a:focus,
a:active {
  color: inherit;
  text-decoration: underline;
}
.socials {
  list-style: none;
  padding: 0;
  margin: 1.5em 0 0;
}
.socials li {
  display: inline-block;
}
.socials li a {
  color: #6b9d8b;
  background: #fff;
  width: 40px;
  height: 40px;
  line-height: 42px;
  text-align: center;
  display: block;
  font-size: 1.5rem;
  margin: 0 .25rem;
  border-radius: 50%;
}
.socials li a:hover,
.socials li a:focus,
.socials li a:active {
  text-decoration: none;
  background: #024731;
  color: #fff;
}
.menus-bg {
  display: inline-block;
  position: relative;
}
.menus-bg:before,
.menus-bg:after {
  content: "";
  position: absolute;
  right: 108%;
  top: 50%;
  margin-top: -108px;
  width: 150px;
  height: 216px;
  background: url(../img/menus-bg-burger.png) center center no-repeat;
  background-size: 100% auto;
}
.menus-bg:after {
  background-image: url(../img/menus-bg-drink.png);
  right: auto;
  left: 108%;
}
.menus-list {
  padding: 0;
  margin: 0;
  list-style: none;
  font-size: 1.3125rem;
}
.menus-list-item {
  padding-top: .75rem;
}
.menus-list-link {
  color: #024731;
  text-decoration: underline;
}
.menus-list-link:hover {
  color: #fecf22;
}
.carousel-indicators {
  bottom: 5px;
}
.carousel-indicators li {
  width: 18px;
  height: 16px;
  border: none;
  opacity: 1;
  background: url(../img/carousel-indicators.png) -21px 0 no-repeat;
  background-size: auto 100%;
}
.carousel-indicators li.active {
  background-position: 0 0;
}
.carousel-control-prev,
.carousel-control-next {
  opacity: 1;
}
.carousel-control-prev .carousel-control-prev-icon,
.carousel-control-next .carousel-control-prev-icon,
.carousel-control-prev .carousel-control-next-icon,
.carousel-control-next .carousel-control-next-icon {
  width: 40px;
  height: 40px;
  background: url(../img/carousel-arrows.png) left top no-repeat;
  background-size: 100% auto;
}
.carousel-control-prev .carousel-control-next-icon,
.carousel-control-next .carousel-control-next-icon {
  background-position: left -40px;
}
.carousel-control-prev:hover,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-next:focus,
.carousel-control-prev:active,
.carousel-control-next:active {
  opacity: 0.9;
}
.sonoma-fly {
  width: 148px;
  height: auto;
  display: block;
  margin: 20px auto 20px;
  position: relative;
  left: -25%;
}
.master-header {
  position: relative;
  z-index: 2;
}
.master-header .logo {
  width: 200px;
}
.master-content > section {
  position: relative;
}
.master-content .soul-join {
  width: 203px;
}
.master-content .logo-yellow {
  width: 210px;
}
.master-content .plants:before {
  content: "";
  width: 40px;
  height: 54px;
  display: block;
  margin: 0 auto;
  margin-bottom: .75rem;
  background: url(../img/plants.png) center center no-repeat;
  background-size: 100% auto;
  transform: rotateY(180deg);
}
.section-intro {
  background: url(../img/sonoma-header-mobile.jpg) center center no-repeat;
  background-size: cover;
  margin-top: -170px;
  padding-top: 150px;
}
.section-intro .intro-title {
  font-family: Arial, Helvetica, sans-serif;
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 0.15em;
}
.section-intro .intro-title img {
  width: 324px;
  height: auto;
  max-width: 100%;
}
.section-gallery:after {
  content: "";
  background: url(../img/soul-stamp.png) center center no-repeat;
  background-size: 100% auto;
  position: absolute;
  left: 50%;
  top: 0;
  width: 85px;
  height: 85px;
  transform: translate(-50%, -50%);
}
body.page h1,
body.page .h1,
body.page h2,
body.page .h2,
body.page h3,
body.page .h3,
body.page h4,
body.page .h4 {
  margin-top: 2rem;
}
.master-footer .made-by {
  opacity: 0.75;
  font-size: .85em;
}
.font-base {
  font-family: "LMMonoPropLt10-Bold", Georgia, 'Times New Roman', serif;
}
.text-primary {
  color: #007b69 !important;
}
.text-primary-dark {
  color: #024731;
}
.text-primary-light {
  color: #6b9d8b;
}
.text-secondary {
  color: #d2492a !important;
}
.text-yellow {
  color: #fecf22;
}
.wide-letters {
  letter-spacing: 1px;
}
.bg-primary {
  background-color: #007b69 !important;
}
.bg-primary-dark {
  background-color: #024731;
}
.bg-primary-light {
  background-color: #6b9d8b;
}
.bg-secondary {
  background-color: #d2492a !important;
}
.bg-bottom {
  background-position: center bottom !important;
}
.bg-top {
  background-position: center top !important;
}
.py-4 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}
.pt-lg-5,
.py-lg-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}
@media screen and (min-width: 370px) {
  .section-intro {
    margin-top: -150px;
    padding-top: 130px;
  }
}
@media screen and (min-width: 768px) {
  .carousel-indicators {
    bottom: 15px;
  }
  .carousel-indicators li {
    width: 23px;
    height: 20px;
    background-position: -26px 0;
  }
  .carousel-indicators li.active {
    background-position: 0 0;
  }
  .carousel-control-prev,
  .carousel-control-next {
    justify-content: flex-start;
  }
  .carousel-control-prev .carousel-control-prev-icon,
  .carousel-control-next .carousel-control-prev-icon,
  .carousel-control-prev .carousel-control-next-icon,
  .carousel-control-next .carousel-control-next-icon {
    margin-left: 20px;
  }
  .carousel-control-prev .carousel-control-next-icon,
  .carousel-control-next .carousel-control-next-icon {
    margin-left: auto;
    margin-right: 20px;
  }
  .carousel-control-next {
    justify-content: flex-end;
  }
  .sonoma-fly {
    left: -15%;
  }
  .section-intro {
    margin-top: -130px;
  }
}
@media screen and (min-width: 992px) {
  .carousel-control-next .carousel-control-next-icon,
  .carousel-control-prev .carousel-control-next-icon,
  .carousel-control-next .carousel-control-prev-icon,
  .carousel-control-prev .carousel-control-prev-icon {
    width: 49px;
    height: 50px;
  }
  .carousel-control-next .carousel-control-next-icon,
  .carousel-control-prev .carousel-control-next-icon {
    background-position: left -50px;
  }
  .master-header .logo {
    width: 325px;
  }
  .sonoma-fly {
    width: 215px;
    position: absolute;
    left: 50%;
    top: 2%;
    margin-left: -390px;
  }
  .your-choice:before {
    content: "";
    position: absolute;
    bottom: 100%;
    left: -15%;
    width: 120%;
    height: 170px;
    background: url(../img/its-your-choice.png) center bottom no-repeat;
    background-size: 100% auto;
  }
  .section-intro {
    background-image: url(../img/sonoma-header.jpg);
    margin-top: -170px;
    padding-top: 170px;
  }
  .section-intro .intro-content {
    padding-top: 150px;
  }
  .master-content .soul-join {
    width: 203px;
  }
  .master-content .logo-yellow {
    width: 394px;
  }
  .section-gallery:after {
    width: 170px;
    height: 170px;
  }
  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}
@media screen and (min-width: 1170px) {
  h1,
  .h1 {
    font-size: 4.375rem;
  }
  h2,
  .h2 {
    font-size: 3.9375rem;
  }
  h3,
  .h3 {
    font-size: 3rem;
  }
  h4,
  .h4 {
    font-size: 2rem;
  }
  .lead {
    font-size: 1.5626rem;
  }
  .big {
    font-size: 3.625rem;
    letter-spacing: 0.02em;
    line-height: 1;
  }
  .socials li a {
    width: 50px;
    height: 50px;
    line-height: 52px;
    margin: 0 .5rem;
    font-size: 1.75rem;
  }
  .menus-list {
    font-size: 1.5626rem;
  }
  .sonoma-fly {
    margin-left: -420px;
  }
  .your-choice:before {
    height: 220px;
  }
  .section-intro {
    background-position: center 20%;
  }
  .section-intro .intro-title {
    position: absolute;
    bottom: 1rem;
  }
  .section-intro .intro-content {
    padding-top: 250px;
  }
  .master-content .plants {
    position: relative;
    display: inline-block;
  }
  .master-content .plants:before {
    position: absolute;
    left: calc(100% + 2.5rem);
    top: -0.3rem;
    width: 90px;
    height: 120px;
    transform: none;
  }
  .master-content .plants:after {
    content: "";
    display: block;
    margin: 0 auto;
    background: url(../img/plants.png) center center no-repeat;
    background-size: 100% auto;
    position: absolute;
    right: calc(100% + 2.5rem);
    top: -0.3rem;
    width: 90px;
    height: 120px;
    transform: rotateY(180deg);
  }
  .py-lg-5 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
}
@media screen and (min-width: 1280px) {
  .your-choice:before {
    background-size: 343px auto;
  }
  .section-intro .container {
    max-width: 1200px;
  }
  .section-intro .intro-content {
    margin-bottom: 80px !important;
  }
  .section-gallery:after {
    width: 233px;
    height: 233px;
  }
}
@media screen and (min-width: 1440px) {
  .section-intro .container {
    max-width: 1280px;
  }
  .section-intro .intro-content [class*="col-"] {
    transform: translateX(90px);
  }
  .section-intro .intro-content [class*="col-"]:nth-of-type(1) {
    transform: translateX(-90px);
  }
}
@media screen and (max-width: 767px) {
}
@media screen and (max-width: 340px) {
  h1,
  .h1 {
    font-size: 2.625rem;
  }
  h2,
  .h2 {
    font-size: 2.125rem;
  }
  h3,
  .h3 {
    font-size: 1.5625rem;
  }
  h4,
  .h4 {
    font-size: 1.25rem;
  }
  .lead {
    font-size: 1.125rem;
  }
  .big {
    font-size: 3rem;
  }
}
@font-face {
  font-family: 'Sonoma';
  src: url('../fonts/Sonoma.eot?xasvm');
  src: url('../fonts/Sonoma.eot?xasvm#iefix') format('embedded-opentype'), url('../fonts/Sonoma.ttf?xasvm') format('truetype'), url('../fonts/Sonoma.woff?xasvm') format('woff'), url('../fonts/Sonoma.svg?xasvm#Sonoma') format('svg');
  font-weight: normal;
  font-style: normal;
}
[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'Sonoma' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-facebook:before {
  content: "\e905";
}
.icon-instagram:before {
  content: "\e907";
}
.icon-pinterest:before {
  content: "\e908";
}
.icon-twitter:before {
  content: "\e909";
}
