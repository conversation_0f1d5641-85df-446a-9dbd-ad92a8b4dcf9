// ========== Mobile first approach ==========

@media screen and (min-width: 370px) {
    .section-bg{
        // min-height: 240px;
    }

    // === Sonoma Fly ===
    .sonoma-fly{
        
    }

    .section-intro{
        margin-top: -150px;
        padding-top: 130px;
    }

     // ===== Utilities =====
    
}

@media screen and (min-width: 768px) {


    // === BS4: Carousel ===
    .carousel-indicators {
        bottom: 15px;
        li{
            width: 23px;
            height: 20px;
            background-position: -26px 0;
            &.active{
                background-position: 0 0;
            }
        }
    }
     
    .carousel-control-prev,
    .carousel-control-next{
        justify-content: flex-start;
        .carousel-control-prev-icon,
        .carousel-control-next-icon{
           margin-left: 20px;
        }
        .carousel-control-next-icon{
            margin-left: auto;
            margin-right: 20px;
        }
    }
    .carousel-control-next{
        justify-content: flex-end;
    }

    .master-content{
    }

    // === Sonoma Fly ===
    .sonoma-fly{
        left: -15%;
    }

    .section-intro{
        margin-top: -130px;
    }

    // .section-bg{
    //     position: absolute;
    //     top: 0;
    //     bottom: 0;
    //     right: 0;
    //     width: 50%;
    // }
    // .section-vibes{
    //     .section-bg{
    //         right: auto;
    //         left: 0;
    //     }
    // }

}

@media screen and (min-width: 992px) {

    // === BS4: Carousel ===
    .carousel-control-next, 
    .carousel-control-prev{

        .carousel-control-next-icon, 
        .carousel-control-prev-icon{
            width: 49px;
            height: 50px;
        }
        .carousel-control-next-icon{
            background-position: left -50px;
        }
    }
    

    // ===== Master header =====
    .master-header{
        .logo{
            width: 325px;
        }
    }

    // === Sonoma Fly ===
    .sonoma-fly{
        width: 215px;
        position: absolute;
        left: 50%;
        top: 2%;
        margin-left: -390px;
    }

    .your-choice{
        &:before{
            content: "";
            position: absolute;
            bottom: 100%;
            left: -15%;
            width: 120%;
            height: 170px;
            background: url(../img/its-your-choice.png) center bottom no-repeat;
            background-size: 100% auto;
        }
    }

    .intro-text{
        // columns: 2;
        // column-gap: 2.5rem;
    }

    .section-intro{
        background-image: url(../img/sonoma-header.jpg);
        margin-top: -170px;
        padding-top: 170px;
        .intro-content{
            padding-top: 150px;
        }
    }
    

    // ===== Master content =====
    .master-content{
        .soul-join{
            width: 203px;
        }
        .logo-yellow{
            width: 394px;
        }
        
    }

    .section-gallery{
        // margin-top: 3rem;
        &:after{
            width: 170px;
            height: 170px;
        }
    }
   
    // ===== Utilities =====
    .py-lg-0{
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

}

@media screen and (min-width: 1170px) {

    h1, .h1{
        font-size: 4.375rem; // 70px
    }
    h2, .h2{
        font-size: 3.9375rem; // 63px
    }
    h3, .h3{
        font-size: 3rem; // 59px
    }
    h4, .h4{
        font-size: 2rem // 32px
    }
    .lead{
        font-size: 1.5626rem; // 25px
    }
    .big{
        font-size: 3.625rem; // 118px
        letter-spacing: 0.02em;
        line-height: 1;
    }

    // === Socials ===
    .socials{
    
        li{
            a{
                width: 50px;
                height: 50px;
                line-height: 52px;
                margin: 0 .5rem;
                font-size: 1.75rem;
            }
        }
    
    }

    // === Menus listing ===
    .menus-list{
        font-size: 1.5626rem;
    }

     // === Sonoma Fly ===
    .sonoma-fly{
        margin-left: -420px;
    }

    .your-choice{
        &:before{
            height: 220px;
        }
    }

    .section-intro{
        background-position: center 20%;
        .intro-title{
            position: absolute;
            bottom: 1rem;
        }
        .intro-content{
            padding-top: 250px;
        }
    }

    .master-content{
        .plants{
            position: relative;
            display: inline-block;
            &:before{
                position: absolute;
                left: ~"calc(100% + 2.5rem)";
                top: -0.3rem;
                width: 90px;
                height: 120px;
                transform: none;
            }
            &:after{
                content: "";
                display: block;
                margin: 0 auto;
                background: url(../img/plants.png) center center no-repeat;
                background-size: 100% auto;
                position: absolute;
                right: ~"calc(100% + 2.5rem)";
                top: -0.3rem;
                width: 90px;
                height: 120px;
                transform: rotateY(180deg);
            }
        }
    }

    // ===== Utilities =====
    .py-lg-5{
        padding-top: 4rem !important;
        padding-bottom: 4rem !important;
    }

}

@media screen and (min-width: 1280px) {

    .your-choice{
        &:before{
            background-size: 343px auto;
        }
    }

    .section-intro{
        .container{
            max-width: 1200px;
        }
        .intro-content{
            margin-bottom: 80px !important;
        }
    }

    .section-gallery{
        // margin-top: 7rem;
        &:after{
            width: 233px;
            height: 233px;
        }
    }
}

@media screen and (min-width: 1440px) {
    .section-intro{
        .container{
            max-width: 1280px;
        }
        .intro-content{
            [class*="col-"]{
                transform: translateX(90px);
            }
            [class*="col-"]:nth-of-type(1){
                transform: translateX(-90px);
            }
        }
    }
}


// ========== Desktop first approach - sorry :) ==========

@media screen and (max-width: 767px) {

    // ===== Master content =====
    .master-content{
        > section{
            // background-color: transparent !important;
        }
    }

}

@media screen and (max-width: 340px) {
    
    h1, .h1{
        font-size: 2.625rem; // 42px
    }
    h2, .h2{
        font-size: 2.125rem; //  34px
    }
    h3, .h3{
        font-size: 1.5625rem; //  25px
    }
    h4, .h4{
        font-size: 1.25rem //   20px
    }
    .lead{
        font-size: 1.125rem; // 18px
    }
    .big{
        font-size: 3rem; // 60px
    }

}