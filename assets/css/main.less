@black: #000;
@white: #fff;

@primary: #007b69;
@primary-dark: #024731;
@primary-light: #6b9d8b;
@secondary: #d2492a;
@yellow: #fecf22;

@font-family-base: "LMMonoPropLt10-Bold", Georgia, 'Times New Roman', serif;
@font-family-headers: "Moon Flower Bold", Arial, Helvetica, sans-serif;

@font-face {
    font-family:"LMMonoPropLt10-Bold";
    src:url("../fonts/lmmonoproplt10-bold.woff2") format("woff2"),url("../fonts/lmmonoproplt10-bold.woff") format("woff"),url("../fonts/lmmonoproplt10-bold.otf") format("opentype");
    font-style:normal;font-weight:400;
}


@font-face {
    font-family:"Moon Flower Bold";
    src:url("../fonts/Moon Flower Bold.woff2") format("woff2"),url("../fonts/Moon Flower Bold.woff") format("woff"),url("../fonts/Moon Flower Bold.otf") format("opentype");
    font-style:normal;font-weight:400;
}

html{
    
}
body{
    font-size: .9375rem;
    color: fade(@black,70%);
    font-family: @font-family-base;
    line-height: 1.33;
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4{
    font-family: @font-family-headers;
    line-height: 1;
    margin-bottom: 0;
}

h1, .h1{
    font-size: 3.125rem; // 50px
}
h2, .h2{
    font-size: 2.5rem; // 40px
}
h3, .h3{
    font-size: 1.875rem; // 30px
}
h4, .h4{
    font-size: 1.3125rem; // 21px
}
.lead{
    font-size: 1.1875rem; // 19px
}
.big{
    font-size: 3.625rem; // 72px
    letter-spacing: 0.02em;
    line-height: 1;
}

a{
    color: inherit;
    text-decoration: none;
    transition: all .35s ease-in-out;
    &:hover,
    &:focus,
    &:active{
        color: inherit;
        text-decoration: underline;
    }
}

// === Socials ===
.socials{
    list-style: none;
    padding: 0;
    margin: 1.5em 0 0;

    li{
        display: inline-block;
        a{
            color: @primary-light;
            background: @white;
            width: 40px;
            height: 40px;
            line-height: 42px;
            text-align: center;
            display: block;
            font-size: 1.5rem;
            margin: 0 .25rem;
            border-radius: 50%;
            &:hover,
            &:focus,
            &:active{
                text-decoration: none;
                background: @primary-dark;
                color: @white;
            }
        }
    }

}

// === Menus listing ===
.menus-bg{
    display: inline-block;
    position: relative;
    &:before,
    &:after{
        content: "";
        position: absolute;
        right: 108%;
        top: 50%;
        margin-top: -108px;
        width: 150px;
        height: 216px;
        background: url(../img/menus-bg-burger.png) center center no-repeat;
        background-size: 100% auto;
    }
    &:after{
        background-image: url(../img/menus-bg-drink.png);
        right: auto;
        left: 108%;
    }
}
.menus-list{
    padding: 0;
    margin: 0;
    list-style: none;
    font-size: 1.3125rem
}
.menus-list-item{
    padding-top: .75rem;
}
.menus-list-link{
    color: @primary-dark;
    text-decoration: underline;
    &:hover{
        color: @yellow;
    }
}

// === BS4: Carousel ===
.carousel-indicators {
    bottom: 5px;
    li{
        width: 18px;
        height: 16px;
        border: none;
        opacity: 1;
        background: url(../img/carousel-indicators.png) -21px 0 no-repeat;
        background-size: auto 100%;
        &.active{
            background-position: 0 0;
        }
    }
}
.carousel-control-prev,
.carousel-control-next{
    opacity: 1;

     
    .carousel-control-prev-icon,
    .carousel-control-next-icon{
        width: 40px;
        height: 40px;
        background: url(../img/carousel-arrows.png) left top no-repeat;
        background-size: 100% auto;
    }
    .carousel-control-next-icon{
        background-position: left -40px;
    }

    &:hover,
    &:focus,
    &:active{
        opacity: 0.9;
    }
}

// === Sonoma Fly ===
.sonoma-fly{
    width: 148px;
    height: auto;
    display: block;
    margin: 20px auto 20px;
    position: relative;
    left: -25%;
}


// ===== Master header =====
.master-header{
    position: relative;
    z-index: 2;
    .logo{
        width: 200px;
    }
}

// ===== Master content =====
.section-bg{
    
}


.master-content{
    > section{
        position: relative;
    }
    .soul-join{
        width: 203px;
    }
    .logo-yellow{
        width: 210px;
    }
    .plants{
        &:before{
            content: "";
            width: 40px;
            height: 54px;
            display: block;
            margin: 0 auto;
            margin-bottom: .75rem;
            background: url(../img/plants.png) center center no-repeat;
            background-size: 100% auto;
            transform: rotateY(180deg);
        }
    }
}

.section-intro{
    background: url(../img/sonoma-header-mobile.jpg) center center no-repeat;
    background-size: cover;
    margin-top: -170px;
    padding-top: 150px;
    .intro-title{
        font-family: Arial, Helvetica, sans-serif;
        text-transform: uppercase;
        font-weight: bold;
        letter-spacing: 0.15em;
        img{
            width: 324px;
            height: auto;
            max-width: 100%;
        }
    }
}

.section-gallery{
    &:after{
        content: "";
        background: url(../img/soul-stamp.png) center center no-repeat;
        background-size: 100% auto;
        position: absolute;
        left: 50%;
        top: 0;
        width: 85px;
        height: 85px;
        transform: translate(-50%,-50%);
    }
}

body.page{
    h1,.h1,
    h2,.h2,
    h3,.h3,
    h4,.h4{
        margin-top: 2rem;
    }
}

// ===== Master footer =====
.master-footer{
    .made-by{
        opacity: 0.75;
        font-size: .85em;
    }
}


// ===== Utilities =====
.font-base{
    font-family: @font-family-base;
}

.text-primary{
    color: @primary !important;
}
.text-primary-dark{
    color: @primary-dark;
}
.text-primary-light{
    color: @primary-light
}
.text-secondary{
    color: @secondary !important;
}
.text-yellow{
    color: @yellow;
}

.wide-letters{
    letter-spacing: 1px;
}

.bg-primary{
    background-color: @primary !important;
}
.bg-primary-dark{
    background-color: @primary-dark;
}
.bg-primary-light{
    background-color: @primary-light;
}
.bg-secondary{
    background-color: @secondary !important;
}

.bg-bottom{
    background-position: center bottom !important;
}
.bg-top{
    background-position: center top !important;
}

.py-4{
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
}
.pt-lg-5, .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
}