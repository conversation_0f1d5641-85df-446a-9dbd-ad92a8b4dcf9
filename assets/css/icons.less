@font-face {
    font-family: 'Sonoma';
    src:  url('../fonts/Sonoma.eot?xasvm');
    src:  url('../fonts/Sonoma.eot?xasvm#iefix') format('embedded-opentype'),
      url('../fonts/Sonoma.ttf?xasvm') format('truetype'),
      url('../fonts/Sonoma.woff?xasvm') format('woff'),
      url('../fonts/Sonoma.svg?xasvm#Sonoma') format('svg');
    font-weight: normal;
    font-style: normal;
  }
  
  [class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'Sonoma' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .icon-facebook:before {
    content: "\e905";
  }
  .icon-instagram:before {
    content: "\e907";
  }
  .icon-pinterest:before {
    content: "\e908";
  }
  .icon-twitter:before {
    content: "\e909";
  }